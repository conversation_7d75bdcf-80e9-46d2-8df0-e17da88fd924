// Code generated by mockery v2.14.0. DO NOT EDIT.

package mocks

import (
	repository "github.com/devtron-labs/devtron/pkg/pipeline/history/repository"
	mock "github.com/stretchr/testify/mock"
)

// CiTemplateHistoryRepository is an autogenerated mock type for the CiTemplateHistoryRepository type
type CiTemplateHistoryRepository struct {
	mock.Mock
}

// Save provides a mock function with given fields: material
func (_m *CiTemplateHistoryRepository) Save(material *repository.CiTemplateHistory) error {
	ret := _m.Called(material)

	var r0 error
	if rf, ok := ret.Get(0).(func(*repository.CiTemplateHistory) error); ok {
		r0 = rf(material)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewCiTemplateHistoryRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewCiTemplateHistoryRepository creates a new instance of CiTemplateHistoryRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewCiTemplateHistoryRepository(t mockConstructorTestingTNewCiTemplateHistoryRepository) *CiTemplateHistoryRepository {
	mock := &CiTemplateHistoryRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
