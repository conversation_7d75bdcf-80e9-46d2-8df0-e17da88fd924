/*
Devtron Labs

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

API version: 1.0.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package openapi

import (
	"encoding/json"
)

// TemplateChartResponse struct for TemplateChartResponse
type TemplateChartResponse struct {
	// helm generated manifest
	Manifest *string `json:"manifest,omitempty"`
}

// NewTemplateChartResponse instantiates a new TemplateChartResponse object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewTemplateChartResponse() *TemplateChartResponse {
	this := TemplateChartResponse{}
	return &this
}

// NewTemplateChartResponseWithDefaults instantiates a new TemplateChartResponse object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewTemplateChartResponseWithDefaults() *TemplateChartResponse {
	this := TemplateChartResponse{}
	return &this
}

// GetManifest returns the Manifest field value if set, zero value otherwise.
func (o *TemplateChartResponse) GetManifest() string {
	if o == nil || o.Manifest == nil {
		var ret string
		return ret
	}
	return *o.Manifest
}

// GetManifestOk returns a tuple with the Manifest field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *TemplateChartResponse) GetManifestOk() (*string, bool) {
	if o == nil || o.Manifest == nil {
		return nil, false
	}
	return o.Manifest, true
}

// HasManifest returns a boolean if a field has been set.
func (o *TemplateChartResponse) HasManifest() bool {
	if o != nil && o.Manifest != nil {
		return true
	}

	return false
}

// SetManifest gets a reference to the given string and assigns it to the Manifest field.
func (o *TemplateChartResponse) SetManifest(v string) {
	o.Manifest = &v
}

func (o TemplateChartResponse) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Manifest != nil {
		toSerialize["manifest"] = o.Manifest
	}
	return json.Marshal(toSerialize)
}

type NullableTemplateChartResponse struct {
	value *TemplateChartResponse
	isSet bool
}

func (v NullableTemplateChartResponse) Get() *TemplateChartResponse {
	return v.value
}

func (v *NullableTemplateChartResponse) Set(val *TemplateChartResponse) {
	v.value = val
	v.isSet = true
}

func (v NullableTemplateChartResponse) IsSet() bool {
	return v.isSet
}

func (v *NullableTemplateChartResponse) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableTemplateChartResponse(val *TemplateChartResponse) *NullableTemplateChartResponse {
	return &NullableTemplateChartResponse{value: val, isSet: true}
}

func (v NullableTemplateChartResponse) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableTemplateChartResponse) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


