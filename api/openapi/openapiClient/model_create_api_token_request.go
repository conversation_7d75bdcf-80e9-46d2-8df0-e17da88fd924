/*
Devtron Labs

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

API version: 1.0.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.
// NOTE : notnull and validate added manually, as auto-generation does not add notnull and validate.

package openapi

import (
	"encoding/json"
)

// CreateApiTokenRequest struct for CreateApiTokenRequest
type CreateApiTokenRequest struct {
	// Name of api-token
	Name *string `json:"name,omitempty,notnull" validate:"required"`
	// Description of api-token
	Description *string `json:"description,omitempty,notnull" validate:"required"`
	// Expiration time of api-token in milliseconds
	ExpireAtInMs *int64 `json:"expireAtInMs,omitempty"`
}

// NewCreateApiTokenRequest instantiates a new CreateApiTokenRequest object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewCreateApiTokenRequest() *CreateApiTokenRequest {
	this := CreateApiTokenRequest{}
	return &this
}

// NewCreateApiTokenRequestWithDefaults instantiates a new CreateApiTokenRequest object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewCreateApiTokenRequestWithDefaults() *CreateApiTokenRequest {
	this := CreateApiTokenRequest{}
	return &this
}

// GetName returns the Name field value if set, zero value otherwise.
func (o *CreateApiTokenRequest) GetName() string {
	if o == nil || o.Name == nil {
		var ret string
		return ret
	}
	return *o.Name
}

// GetNameOk returns a tuple with the Name field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CreateApiTokenRequest) GetNameOk() (*string, bool) {
	if o == nil || o.Name == nil {
		return nil, false
	}
	return o.Name, true
}

// HasName returns a boolean if a field has been set.
func (o *CreateApiTokenRequest) HasName() bool {
	if o != nil && o.Name != nil {
		return true
	}

	return false
}

// SetName gets a reference to the given string and assigns it to the Name field.
func (o *CreateApiTokenRequest) SetName(v string) {
	o.Name = &v
}

// GetDescription returns the Description field value if set, zero value otherwise.
func (o *CreateApiTokenRequest) GetDescription() string {
	if o == nil || o.Description == nil {
		var ret string
		return ret
	}
	return *o.Description
}

// GetDescriptionOk returns a tuple with the Description field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CreateApiTokenRequest) GetDescriptionOk() (*string, bool) {
	if o == nil || o.Description == nil {
		return nil, false
	}
	return o.Description, true
}

// HasDescription returns a boolean if a field has been set.
func (o *CreateApiTokenRequest) HasDescription() bool {
	if o != nil && o.Description != nil {
		return true
	}

	return false
}

// SetDescription gets a reference to the given string and assigns it to the Description field.
func (o *CreateApiTokenRequest) SetDescription(v string) {
	o.Description = &v
}

// GetExpireAtInMs returns the ExpireAtInMs field value if set, zero value otherwise.
func (o *CreateApiTokenRequest) GetExpireAtInMs() int64 {
	if o == nil || o.ExpireAtInMs == nil {
		var ret int64
		return ret
	}
	return *o.ExpireAtInMs
}

// GetExpireAtInMsOk returns a tuple with the ExpireAtInMs field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CreateApiTokenRequest) GetExpireAtInMsOk() (*int64, bool) {
	if o == nil || o.ExpireAtInMs == nil {
		return nil, false
	}
	return o.ExpireAtInMs, true
}

// HasExpireAtInMs returns a boolean if a field has been set.
func (o *CreateApiTokenRequest) HasExpireAtInMs() bool {
	if o != nil && o.ExpireAtInMs != nil {
		return true
	}

	return false
}

// SetExpireAtInMs gets a reference to the given int64 and assigns it to the ExpireAtInMs field.
func (o *CreateApiTokenRequest) SetExpireAtInMs(v int64) {
	o.ExpireAtInMs = &v
}

func (o CreateApiTokenRequest) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Name != nil {
		toSerialize["name"] = o.Name
	}
	if o.Description != nil {
		toSerialize["description"] = o.Description
	}
	if o.ExpireAtInMs != nil {
		toSerialize["expireAtInMs"] = o.ExpireAtInMs
	}
	return json.Marshal(toSerialize)
}

type NullableCreateApiTokenRequest struct {
	value *CreateApiTokenRequest
	isSet bool
}

func (v NullableCreateApiTokenRequest) Get() *CreateApiTokenRequest {
	return v.value
}

func (v *NullableCreateApiTokenRequest) Set(val *CreateApiTokenRequest) {
	v.value = val
	v.isSet = true
}

func (v NullableCreateApiTokenRequest) IsSet() bool {
	return v.isSet
}

func (v *NullableCreateApiTokenRequest) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableCreateApiTokenRequest(val *CreateApiTokenRequest) *NullableCreateApiTokenRequest {
	return &NullableCreateApiTokenRequest{value: val, isSet: true}
}

func (v NullableCreateApiTokenRequest) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableCreateApiTokenRequest) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


