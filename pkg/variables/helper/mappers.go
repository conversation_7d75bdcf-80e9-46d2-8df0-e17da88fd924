/*
 * Copyright (c) 2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package helper

import (
	"github.com/devtron-labs/devtron/pkg/resourceQualifiers"
	"github.com/devtron-labs/devtron/pkg/variables/models"
)

func GetQualifierId(attributeType models.AttributeType) resourceQualifiers.Qualifier {
	switch attributeType {
	case models.Global:
		return resourceQualifiers.GLOBAL_QUALIFIER
	default:
		return 0
	}
}

func GetAttributeType(qualifier resourceQualifiers.Qualifier) models.AttributeType {
	switch qualifier {
	case resourceQualifiers.GLOBAL_QUALIFIER:
		return models.Global
	default:
		return ""
	}
}

func GetIdentifierTypeFromAttributeType(attribute models.AttributeType) []models.IdentifierType {
	switch attribute {
	default:
		return nil
	}
}
