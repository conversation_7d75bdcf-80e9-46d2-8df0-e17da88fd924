openapi: 3.0.3
info:
  title: Infra Config
  description: API SPEC for Infra Configurations
  version: 1.0.0
servers:
  - url: 'https'
paths:
#  send 404 responses if resource doesn't exist
  /orchestrator/infra-config/profile/{name}:
    get:
      description: Get Infra Profile by name
      responses:
        "200":
          description: gets the infra config profile by its name.
          content:
            application/json:
              schema:
                  $ref: "#/components/schemas/ProfileResponse"
    put:
      description: Update Infra Profile
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Profile'
      responses:
        "200":
          description: creates a infra config profile.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Profile"


components:
  schemas:
    Unit:
      type: object
      properties:
        name:
          type: string
          description: Unit Name
          example: "mi"
        conversionFactor:
          type: number
          description: Conversion Factor to convert to base unit
          example: 1

    ConfigurationUnits:
        type: object
        properties:
          name:
            type: string
            description: Configuration Units
          units:
            type: array
            description: Configuration Units
            items:
              $ref: '#/components/schemas/Unit'
    ProfileResponse:
      type: object
      properties:
        configurationUnits:
          type: array
          description: Configuration Units
          items:
            $ref: '#/components/schemas/ConfigurationUnits'
        defaultConfigurations:
          type: array
          description: Default Configurations
          items:
            $ref: '#/components/schemas/Configuration'
        profile:
          $ref: '#/components/schemas/Profile'






    Configuration:
      type: object
      properties:
        id:
          type: integer
          description: Property Id
          example: 1
        key:
          type: string
          description: Property Name
          required: true,
          example: "cpu_limits"
        value:
          required: true,
          type: string
          description: Property Value
          example: "0.5"
        profileName:
          type: string
          description: Profile Name
          example: "java"
        unit:
          type: string
          description: Property Unit
          example: "m"
        active:
          type: boolean
          description: Property Active
          example: true

    Profile:
      type: object
      properties:
          id:
            type: integer
            description: Profile Id
            example: 1
          name:
            type: string
            description: Profile Name
            example: "java"
          description:
            type: string
            description: Profile Description
            example: "all java apps should have this infra profile"
          type:
            type: string
            description: type of profile "eg:0,1,2"
            example: DEFAULT,NORMAL,CUSTOM
          configurations:
            type: array
            description: Profile Configurations
            items:
              $ref: '#/components/schemas/Configuration'
          appCount:
            readOnly: true
            type: integer
            description: Number of apps using this profile
            example: 1
          createdAt:
            required: false
            type: string
            description: Profile Created At
            example: "2021-06-01T06:30:00.000Z"
          updatedAt:
            type: string
            description: Profile Updated At
            example: "2021-06-01T06:30:00.000Z"
          createdBy:
            type: integer
            description: Profile Created By
            example: 1
          updatedBy:
            type: integer
            description: Profile Updated By
            example: 1

  
