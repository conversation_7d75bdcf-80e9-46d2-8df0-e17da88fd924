openapi: "3.0.0"
info:
  version: 1.0.0
  title: <PERSON><PERSON>
paths:
  /user/role/group/v2:
    get:
      summary: Returns all role groups
      description: all the template group policies
      operationId: findGroupPolicy
      parameters:
        - name: searchKey
          in: query
          description: Search key for group listing
          required: false
          schema:
            type: string

        - name: sortOrder
          in: query
          description: Sorting order (ASC or DESC)
          required: false
          schema:
            type: string
            enum:
              - ASC
              - DESC

        - name: sortBy
          in: query
          description: Sorting by name
          required: false
          schema:
            type: string
            enum:
              - name

        - name: offset
          in: query
          description: Offset for paginating the results
          required: false
          schema:
            type: integer

        - name: size
          in: query
          description: Size of the result set
          required: false
          schema:
            type: integer

        - name: showAll
          in: query
          description: Show all Role groups (boolean)
          required: false
          schema:
            type: boolean

      responses:
        '200':
          description: list response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RoleGroupListingResponse'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /user/role/group:
    get:
      summary: Returns all role groups
      description: all the template group policies
      operationId: findGroupPolicy
      deprecated: true   # Marking the operation as deprecated
      responses:
        '200':
          description: list response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RoleGroup'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    post:
      summary: Creates a new Role Group
      description: create chart group api, with multiple environment in one row of policy, plus chart group additional type of policy.
      operationId: addGroupPolicy
      requestBody:
        description: json as request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RoleGroup'
      responses:
        '200':
          description: create group policy response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RoleGroup'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    put:
      summary: update a role group
      description: update a role group in the system.
      operationId: updateGroupPolicy
      requestBody:
        description: json as request body
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/RoleGroup'
                - type: object
                  required:
                    - id
                  properties:
                    id:
                      type: integer
                      format: int64
      responses:
        '200':
          description: group response
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/RoleGroup'
                  - type: object
                    properties:
                      id:
                        type: integer
                        format: int64

        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /user/role/group/search:
    get:
      summary: search a role group by NAME
      description: search role group by group name
      operationId: findRoleGroupByName
      parameters:
        - name: name
          in: query
          description: json as request body
          required: true
          schema:
            type: string
      responses:
        '200':
          description: list response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RoleGroup'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /user/role/group/{id}:
    delete:
      summary: Deletes a group policy by ID
      description: deletes a single group policy based on the ID supplied
      operationId: deleteGroupPolicy
      parameters:
        - name: id
          in: path
          description: ID of group policy
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '204':
          description: group deleted
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /user/role/group/bulk:
    delete:
      summary: Delete multiple permission groups in bulk
      description: Deletes permission groups entities in bulk based on the provided criteria.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkDeleteRequest'
      responses:
        '200':
          description: Successfully deleted groups
          content:
            application/json:
              schema:
                type: boolean
        '400':
          description: Bad request, invalid input
        '404':
          description: groups not found
        '500':
          description: Internal server error

components:
  schemas:
    RoleGroup:
      type: object
      required:
        - name
      properties:
        id:
          type: integer
          description: Unique id of role group
        name:
          type: string
          description: Unique name of group
        description:
          type: string
          description: description
        roleFilters:
          type: array
          items:
            $ref: '#/components/schemas/roleFilter'
          description: role filters objects


    roleFilter:
      type: object
      required:
        - action
      properties:
        entity:
          type: string
          description: global entity name, i.e chart-group, git, docker, cluster etc. if this key is present than field team, application and environment are ignored.
          enum:
            - chart-group
            - docker
            - git
            - cluster
            - notification
        team:
          type: string
          description: team name
        entityName:
          type: string
          description: global entity name item name, i.e chart-group ("abc"), git("devtron-gt") etc.
        environment:
          type: string
          description: comma saperated environments names.
        action:
          type: string
          description: action is type of role, i.e manager, admin, trigger, view, etc.
        accessType:
          type: string
          enum: ["", "helm-app"]
          description: accessType difine permission type dawf=devtron app work flow, helm-app=helm app work flow. based on this flag data categoriesed into devtron and helm permission tabs in user auth role group section.


    Error:
      required:
        - code
        - message
      properties:
        code:
          type: integer
          format: int32
          description: Error code
        message:
          type: string
          description: Error message

    RoleGroupListingResponse:
      type: object
      properties:
        roleGroups:
          items:
            $ref: '#/components/schemas/RoleGroup'
          description: role groups listing
        totalCount:
          type: integer
          description: total number of results satisfying the conditions

    BulkDeleteRequest:
      type: object
      properties:
        ids:
          type: array
          items:
            type: integer
            format: int32
          description: Array of group IDs to be deleted
        listingRequest:
          $ref: '#/components/schemas/ListingRequest'
    ListingRequest:
      type: object
      properties:
        searchKey:
          type: string
          description: Search key for filtering
        sortOrder:
          type: string
          enum:
            - ASC
            - DESC
          description: Sorting order
        sortBy:
          type: string
          enum:
            - name
          description: Attribute to sort by
        offset:
          type: integer
          format: int32
          description: Starting index for fetching listings
        size:
          type: integer
          format: int32
          description: Number of listings to fetch
        showAll:
          type: boolean
          description: Show all listings