# AppEnvironmentDetail
## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**environmentName** | **String** | name of the environemnt | [optional] [default to null]
**environmentId** | **Integer** | id in which app is deployed | [optional] [default to null]
**namespace** | **String** | namespace corresponding to the environemnt | [optional] [default to null]
**isPrduction** | **Boolean** | if given environemnt is marked as production or not, nullable | [optional] [default to null]
**clusterName** | **String** | cluster corresponding to the environemt where application is deployed | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)

