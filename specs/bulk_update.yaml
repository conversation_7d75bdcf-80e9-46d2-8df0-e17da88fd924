openapi: "3.0.0"
info:
  version: 1.0.0
  title: Bulk Update - Deployment Template, ConfigMaps, Secrets
servers:
  - url: http://localhost:3000/orchestrator/batch
paths:
  /{apiVersion}/{kind}/readme:
    get:
      description: Returns Readme for bulk update for different resource in the url
      operationId: FindBulkUpdateReadme
      parameters:
        - name: apiVersion
          in: path
          required: true
          schema:
            type: string
        - name: kind
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful GET operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkUpdateSeeExampleResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /v1beta1/application/dryrun:
    post:
      description: Returns details(id, name, envId) of all apps to be impacted with bulk update
      operationId: GetBulkAppName
      requestBody:
        description: A JSON object containing information by which apps will be filtered
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkUpdateScript'
      responses:
        '200':
          description: Successfully return all impacted app details.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ImpactedObjectsResponse'
        '400':
          description: Bad Request. Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /v1beta1/application:
    post:
      description: Bulk Updates all impacted apps
      operationId: BulkUpdate
      requestBody:
        description: A JSON object containing information about update changes and by which apps will be filtered
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkUpdateScript'
      responses:
        '200':
          description: Successfully updated all impacted apps.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkUpdateResponse'
        '400':
          description: Bad Request. Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

components:
  schemas:
    BulkUpdateSeeExampleResponse:
      type: object
      required:
        - Script
      properties:
        resource:
          type: string
          description: Resource from url path, i.e. {apiVersion} & {kind}
        script:
          $ref: '#/components/schemas/BulkUpdateScript'
          description: Input Script for bulk update
        readMe:
          type: string
          description: Readme for bulk update
    BulkUpdateScript:
      type: object
      required:
        - ApiVersion
        - Kind
        - Spec
      properties:
        apiVersion:
          type: string
          description: Api version from url
          example:
            - v1beta1
        kind:
          type: string
          description: Kind
          example:
            - application
        spec:
          $ref: '#/components/schemas/BulkUpdatePayload'
    BulkUpdatePayload:
      type: object
      properties:
        includes:
          $ref: '#/components/schemas/NameIncludesExcludes'
        excludes:
          $ref: '#/components/schemas/NameIncludesExcludes'
        envIds:
          type: array
          items:
            type: integer
          description: All Env Id's for updating dependent apps
        global:
          type: boolean
          description: Global flag for updating dependent apps
        DeploymentTemplate:
          $ref: '#/components/schemas/Tasks'
        ConfigMaps:
          type: object
          properties:
            names:
              type: array
              items:
                type: string
                description : Name of All ConfigMaps to be updated
            tasks:
              $ref: '#/components/schemas/Spec'
        Secrets:
          type: object
          properties:
            names:
              type: array
              items:
                type: string
                description : Name of All Secrets to be updated
            tasks:
              $ref: '#/components/schemas/Spec'
    Tasks:
      type: object
      properties:
        spec:
          $ref: '#/components/schemas/Spec'
          description: Spec of the Task
    Spec:
      type: object
      properties:
        patchData:
          type: string
          description: string with details of the patch to be used for updating

    NameIncludesExcludes:
      type: object
      properties:
        names:
          type: array
          items:
            type: string
          description: All strings of app names to be included/excluded
    ImpactedObjectsResponse:
      type: object
      properties:
        deploymentTemplate:
          type: array
          items:
            $ref: '#/components/schemas/DeploymentTemplateImpactedObjectsResponseForOneApp'
        configMap:
          type: array
          items:
            $ref: '#/components/schemas/CmAndSecretImpactedObjectsResponseForOneApp'
        secret:
          type: array
          items:
            $ref: '#/components/schemas/CmAndSecretImpactedObjectsResponseForOneApp'
    DeploymentTemplateImpactedObjectsResponseForOneApp:
      type: object
      properties:
        appId:
          type: integer
          description: Id of the impacted app
        appName:
          type: string
          description: Name of the impacted app
        envId:
          type: string
          description: Env Id of the impacted app
    CmAndSecretImpactedObjectsResponseForOneApp:
      type: object
      properties:
        appId:
          type: integer
          description: Id of the impacted app
        appName:
          type: string
          description: Name of the impacted app
        envId:
          type: string
          description: Env Id of the impacted app
        names:
          type: array
          items:
            type: string
            description: Names of all configmaps/secrets impacted
    BulkUpdateResponse:
      type: object
      properties:
        deploymentTemplate:
          $ref: '#/components/schemas/DeploymentTemplateBulkUpdateResponse'
        configMap:
          $ref: '#/components/schemas/CmAndSecretBulkUpdateResponse'
        secret:
          $ref: '#/components/schemas/CmAndSecretBulkUpdateResponse'
    DeploymentTemplateBulkUpdateResponse:
      type: object
      properties:
        message:
          type: array
          items:
            type: string
          description: All top-level messages in response of bulk update action
        failure:
          type: array
          items:
            $ref: '#/components/schemas/DeploymentTemplateBulkUpdateResponseForOneApp'
          description: Details of all apps on which bulk update failed
        successful:
          type: array
          items:
            $ref: '#/components/schemas/DeploymentTemplateBulkUpdateResponseForOneApp'
          description: Details of all apps on which bulk update applied successfully
    CmAndSecretBulkUpdateResponse:
      type: object
      properties:
        message:
          type: array
          items:
            type: string
          description: All top-level messages in response of bulk update action
        failure:
          type: array
          items:
            $ref: '#/components/schemas/CmAndSecretBulkUpdateResponseForOneApp'
          description: Details of all apps on which bulk update failed
        successful:
          type: array
          items:
            $ref: '#/components/schemas/CmAndSecretBulkUpdateResponseForOneApp'
          description: Details of all apps on which bulk update applied successfully
    DeploymentTemplateBulkUpdateResponseForOneApp:
      type: object
      properties:
        appId:
          type: integer
          description: Id of the concerned app
        appName:
          type: string
          description: Name of the concerned app
        envId:
          type: integer
          description: Env ID of the concerned app
        message:
          type: string
          description: App-level message for the concerned app
    CmAndSecretBulkUpdateResponseForOneApp:
      type: object
      properties:
        appId:
          type: integer
          description: Id of the concerned app
        appName:
          type: string
          description: Name of the concerned app
        envId:
          type: integer
          description: Env ID of the concerned app
        names:
          type: array
          items:
            type: string
          description: Names of all configmaps/secrets
        message:
          type: string
          description: App-level message for the concerned app

    Error:
      required:
        - code
        - message
      properties:
        code:
          type: integer
          description: Error code
        message:
          type: string
          description: Error message