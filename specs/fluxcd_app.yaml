openapi: 3.0.0
info:
  title: Flux Application Orchestrator API
  version: 1.0.0
paths:
  /orchestrator/flux-application:
    get:
      summary: List Flux Applications
      operationId: listFluxApplications
      parameters:
        - name: token
          in: header
          required: true
          schema:
            type: string
          description: Authorization token
        - name: clusterIds
          in: query
          required: true
          schema:
            type: string
          description: Comma-separated list of cluster IDs.
      responses:
        '200':
          description: List of Flux applications
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AppListDto'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /orchestrator/flux-application/app:
      get:
        summary: Get application details
        description: Retrieve details of a specific Flux application.
        parameters:
          - name: appId
            in: query
            required: true
            schema:
              type: string
            description: The application identifier in the format "1|default|myksApp|Kustomization as the first field having  the cluster id, then second field having the namespace of the app , third field denoted the app name and last contains a boolean value of true and false".
          - name: token
            in: header
            required: true
            schema:
              type: string
            description: The authentication token.
        responses:
          '200':
            description: Successful response
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/FluxApplicationDetailDto'
          '400':
            description: Bad request
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/Error'
          '403':
            description: Forbidden
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/Error'
          '500':
            description: Internal server error
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/Error'


components:
  schemas:
    AppListDto:
      type: object
      properties:
        clusterIds:
          type: array
          description: Cluster Id to which the result corresponds
          example: [1]
          items:
            type: integer
        fluxApplication:
          type: array
          description: List of Flux applications
          items:
            $ref: '#/components/schemas/FluxAppDto'
    FluxAppDto:
      type: object
      properties:
        appName:
          type: string
          description: Name of the application
          example: flux-system
        appStatus:
          type: boolean
          enum: [True, False]
          description:
          example: True
        syncStatus:
          type: string
          description: Sync status of the application
          example: "Applied revision: main@sha1:a3c3de4083eca4ca01d63f9f1b07599b64f3f8ca"
        clusterId:
          type: integer
          description: ID of the cluster
          example: 2
        clusterName:
          type: string
          description: Name of the cluster
          example: test-cluster-1
        namespace:
          type: string
          description: Namespace of the application
          example: flux-system
        fluxAppDeploymentType:
          type: string
          enum": ["Kustomization", "HelmRelease"]
          description: Indicates if the application is a Kustomize type or standalone flux made HelmRelease app
          example: true
    FluxAppStatusDetail:
      type: object
      properties:
        status:
          type: string
          description: Tells about the status whether true or false of the last action performed
        message:
          type: string
          description: Brief message of the last encountered reason
        reason:
          type: string
          description: Short key words like 'ReconciliationFailed', 'Reconciled', and so on for the user to understand the reason of the given of the status

    InfoItem:
      type: object
      properties:
        name:
          type: string
        value:
          type: string

    HealthStatus:
      type: object
      properties:
        status:
          type: string
        message:
          type: string

    ResourceNetworkingInfo:
      type: object
      properties:
        labels:
          type: object
          additionalProperties:
            type: string

    ResourceRef:
      type: object
      properties:
        group:
          type: string
        version:
          type: string
        kind:
          type: string
        namespace:
          type: string
        name:
          type: string
        uid:
          type: string

    PodMetadata:
      type: object
      properties:
        name:
          type: string
        uid:
          type: string
        containers:
          type: array
          items:
            type: string
        initContainers:
          type: array
          items:
            type: string
        isNew:
          type: boolean
        ephemeralContainers:
          type: array
          items:
            $ref: '#/components/schemas/EphemeralContainerData'

    EphemeralContainerData:
      type: object
      properties:
        name:
          type: string
        isExternal:
          type: boolean

    ResourceNode:
      type: object
      properties:
        group:
          type: string
        version:
          type: string
        kind:
          type: string
        namespace:
          type: string
        name:
          type: string
        uid:
          type: string
        parentRefs:
          type: array
          items:
            $ref: '#/components/schemas/ResourceRef'
        networkingInfo:
          $ref: '#/components/schemas/ResourceNetworkingInfo'
        resourceVersion:
          type: string
        health:
          $ref: '#/components/schemas/HealthStatus'
        isHibernated:
          type: boolean
        canBeHibernated:
          type: boolean
        info:
          type: array
          items:
            $ref: '#/components/schemas/InfoItem'
        createdAt:
          type: string
          format: date-time
        port:
          type: array
          items:
            type: integer
        isHook:
          type: boolean
        hookType:
          type: string

    ResourceTreeResponse:
      type: object
      properties:
        nodes:
          type: array
          items:
            $ref: '#/components/schemas/ResourceNode'
        podMetadata:
          type: array
          items:
            $ref: '#/components/schemas/PodMetadata'

    FluxApplicationDetailDto:
      type: object
      properties:
        FluxApplication:
          $ref: '#/components/schemas/FluxAppDto'
        FluxAppStatusDetail:
          $ref: '#/components/schemas/FluxAppStatusDetail'
        ResourceTreeResponse:
          $ref: '#/components/schemas/ResourceTreeResponse'

    Error:
      type: object
      properties:
        message:
          type: string
          description: Error message
          example: unauthorized
