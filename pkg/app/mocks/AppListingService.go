// Code generated by mockery v2.31.4. DO NOT EDIT.

package mocks

import (
	bean "github.com/devtron-labs/devtron/api/bean/AppView"
	app "github.com/devtron-labs/devtron/pkg/app"

	context "context"

	http "net/http"

	mock "github.com/stretchr/testify/mock"

	v1 "github.com/prometheus/client_golang/api/prometheus/v1"
)

// AppListingService is an autogenerated mock type for the AppListingService type
type AppListingService struct {
	mock.Mock
}

// BuildAppListingResponse provides a mock function with given fields: fetchAppListingRequest, envContainers
func (_m *AppListingService) BuildAppListingResponse(fetchAppListingRequest app.FetchAppListingRequest, envContainers []*bean.AppEnvironmentContainer) ([]*bean.AppContainer, error) {
	ret := _m.Called(fetchAppListingRequest, envContainers)

	var r0 []*bean.AppContainer
	var r1 error
	if rf, ok := ret.Get(0).(func(app.FetchAppListingRequest, []*bean.AppEnvironmentContainer) ([]*bean.AppContainer, error)); ok {
		return rf(fetchAppListingRequest, envContainers)
	}
	if rf, ok := ret.Get(0).(func(app.FetchAppListingRequest, []*bean.AppEnvironmentContainer) []*bean.AppContainer); ok {
		r0 = rf(fetchAppListingRequest, envContainers)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*bean.AppContainer)
		}
	}

	if rf, ok := ret.Get(1).(func(app.FetchAppListingRequest, []*bean.AppEnvironmentContainer) error); ok {
		r1 = rf(fetchAppListingRequest, envContainers)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BuildAppListingResponseV2 provides a mock function with given fields: fetchAppListingRequest, envContainers
func (_m *AppListingService) BuildAppListingResponseV2(fetchAppListingRequest app.FetchAppListingRequest, envContainers []*bean.AppEnvironmentContainer) ([]*bean.AppContainer, error) {
	ret := _m.Called(fetchAppListingRequest, envContainers)

	var r0 []*bean.AppContainer
	var r1 error
	if rf, ok := ret.Get(0).(func(app.FetchAppListingRequest, []*bean.AppEnvironmentContainer) ([]*bean.AppContainer, error)); ok {
		return rf(fetchAppListingRequest, envContainers)
	}
	if rf, ok := ret.Get(0).(func(app.FetchAppListingRequest, []*bean.AppEnvironmentContainer) []*bean.AppContainer); ok {
		r0 = rf(fetchAppListingRequest, envContainers)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*bean.AppContainer)
		}
	}

	if rf, ok := ret.Get(1).(func(app.FetchAppListingRequest, []*bean.AppEnvironmentContainer) error); ok {
		r1 = rf(fetchAppListingRequest, envContainers)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CpuRequestGroupByContainer provides a mock function with given fields: podName, namespace, env, proEndpoint
func (_m *AppListingService) CpuRequestGroupByContainer(podName string, namespace string, env string, proEndpoint string) map[string]string {
	ret := _m.Called(podName, namespace, env, proEndpoint)

	var r0 map[string]string
	if rf, ok := ret.Get(0).(func(string, string, string, string) map[string]string); ok {
		r0 = rf(podName, namespace, env, proEndpoint)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]string)
		}
	}

	return r0
}

// CpuRequestGroupByPod provides a mock function with given fields: namespace, env, proEndpoint
func (_m *AppListingService) CpuRequestGroupByPod(namespace string, env string, proEndpoint string) map[string]string {
	ret := _m.Called(namespace, env, proEndpoint)

	var r0 map[string]string
	if rf, ok := ret.Get(0).(func(string, string, string) map[string]string); ok {
		r0 = rf(namespace, env, proEndpoint)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]string)
		}
	}

	return r0
}

// CpuUsageGroupByContainer provides a mock function with given fields: podName, namespace, env, proEndpoint
func (_m *AppListingService) CpuUsageGroupByContainer(podName string, namespace string, env string, proEndpoint string) map[string]string {
	ret := _m.Called(podName, namespace, env, proEndpoint)

	var r0 map[string]string
	if rf, ok := ret.Get(0).(func(string, string, string, string) map[string]string); ok {
		r0 = rf(podName, namespace, env, proEndpoint)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]string)
		}
	}

	return r0
}

// CpuUsageGroupByPod provides a mock function with given fields: namespace, env, proEndpoint
func (_m *AppListingService) CpuUsageGroupByPod(namespace string, env string, proEndpoint string) map[string]string {
	ret := _m.Called(namespace, env, proEndpoint)

	var r0 map[string]string
	if rf, ok := ret.Get(0).(func(string, string, string) map[string]string); ok {
		r0 = rf(namespace, env, proEndpoint)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]string)
		}
	}

	return r0
}

// CpuUsageGroupByPodGraph provides a mock function with given fields: podName, namespace, env, proEndpoint, r
func (_m *AppListingService) CpuUsageGroupByPodGraph(podName string, namespace string, env string, proEndpoint string, r v1.Range) map[string][]interface{} {
	ret := _m.Called(podName, namespace, env, proEndpoint, r)

	var r0 map[string][]interface{}
	if rf, ok := ret.Get(0).(func(string, string, string, string, v1.Range) map[string][]interface{}); ok {
		r0 = rf(podName, namespace, env, proEndpoint, r)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string][]interface{})
		}
	}

	return r0
}

// FetchAllDevtronManagedApps provides a mock function with given fields:
func (_m *AppListingService) FetchAllDevtronManagedApps() ([]app.AppNameTypeIdContainer, error) {
	ret := _m.Called()

	var r0 []app.AppNameTypeIdContainer
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]app.AppNameTypeIdContainer, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []app.AppNameTypeIdContainer); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]app.AppNameTypeIdContainer)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchAppDetails provides a mock function with given fields: ctx, appId, envId
func (_m *AppListingService) FetchAppDetails(ctx context.Context, appId int, envId int) (bean.AppDetailContainer, error) {
	ret := _m.Called(ctx, appId, envId)

	var r0 bean.AppDetailContainer
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int, int) (bean.AppDetailContainer, error)); ok {
		return rf(ctx, appId, envId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int, int) bean.AppDetailContainer); ok {
		r0 = rf(ctx, appId, envId)
	} else {
		r0 = ret.Get(0).(bean.AppDetailContainer)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int, int) error); ok {
		r1 = rf(ctx, appId, envId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchAppStageStatus provides a mock function with given fields: appId, appType
func (_m *AppListingService) FetchAppStageStatus(appId int, appType int) ([]bean.AppStageStatus, error) {
	ret := _m.Called(appId, appType)

	var r0 []bean.AppStageStatus
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int) ([]bean.AppStageStatus, error)); ok {
		return rf(appId, appType)
	}
	if rf, ok := ret.Get(0).(func(int, int) []bean.AppStageStatus); ok {
		r0 = rf(appId, appType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]bean.AppStageStatus)
		}
	}

	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(appId, appType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchAppTriggerView provides a mock function with given fields: appId
func (_m *AppListingService) FetchAppTriggerView(appId int) ([]bean.TriggerView, error) {
	ret := _m.Called(appId)

	var r0 []bean.TriggerView
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]bean.TriggerView, error)); ok {
		return rf(appId)
	}
	if rf, ok := ret.Get(0).(func(int) []bean.TriggerView); ok {
		r0 = rf(appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]bean.TriggerView)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchAppsByEnvironment provides a mock function with given fields: fetchAppListingRequest, w, r, token, apiVersion
func (_m *AppListingService) FetchAppsByEnvironment(fetchAppListingRequest app.FetchAppListingRequest, w http.ResponseWriter, r *http.Request, token string, apiVersion string) ([]*bean.AppEnvironmentContainer, error) {
	ret := _m.Called(fetchAppListingRequest, w, r, token, apiVersion)

	var r0 []*bean.AppEnvironmentContainer
	var r1 error
	if rf, ok := ret.Get(0).(func(app.FetchAppListingRequest, http.ResponseWriter, *http.Request, string, string) ([]*bean.AppEnvironmentContainer, error)); ok {
		return rf(fetchAppListingRequest, w, r, token, apiVersion)
	}
	if rf, ok := ret.Get(0).(func(app.FetchAppListingRequest, http.ResponseWriter, *http.Request, string, string) []*bean.AppEnvironmentContainer); ok {
		r0 = rf(fetchAppListingRequest, w, r, token, apiVersion)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*bean.AppEnvironmentContainer)
		}
	}

	if rf, ok := ret.Get(1).(func(app.FetchAppListingRequest, http.ResponseWriter, *http.Request, string, string) error); ok {
		r1 = rf(fetchAppListingRequest, w, r, token, apiVersion)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchAppsByEnvironmentV2 provides a mock function with given fields: fetchAppListingRequest, w, r, token
func (_m *AppListingService) FetchAppsByEnvironmentV2(fetchAppListingRequest app.FetchAppListingRequest, w http.ResponseWriter, r *http.Request, token string) ([]*bean.AppEnvironmentContainer, int, error) {
	ret := _m.Called(fetchAppListingRequest, w, r, token)

	var r0 []*bean.AppEnvironmentContainer
	var r1 int
	var r2 error
	if rf, ok := ret.Get(0).(func(app.FetchAppListingRequest, http.ResponseWriter, *http.Request, string) ([]*bean.AppEnvironmentContainer, int, error)); ok {
		return rf(fetchAppListingRequest, w, r, token)
	}
	if rf, ok := ret.Get(0).(func(app.FetchAppListingRequest, http.ResponseWriter, *http.Request, string) []*bean.AppEnvironmentContainer); ok {
		r0 = rf(fetchAppListingRequest, w, r, token)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*bean.AppEnvironmentContainer)
		}
	}

	if rf, ok := ret.Get(1).(func(app.FetchAppListingRequest, http.ResponseWriter, *http.Request, string) int); ok {
		r1 = rf(fetchAppListingRequest, w, r, token)
	} else {
		r1 = ret.Get(1).(int)
	}

	if rf, ok := ret.Get(2).(func(app.FetchAppListingRequest, http.ResponseWriter, *http.Request, string) error); ok {
		r2 = rf(fetchAppListingRequest, w, r, token)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// FetchJobs provides a mock function with given fields: fetchJobListingRequest
func (_m *AppListingService) FetchJobs(fetchJobListingRequest app.FetchAppListingRequest) ([]*bean.JobContainer, error) {
	ret := _m.Called(fetchJobListingRequest)

	var r0 []*bean.JobContainer
	var r1 error
	if rf, ok := ret.Get(0).(func(app.FetchAppListingRequest) ([]*bean.JobContainer, error)); ok {
		return rf(fetchJobListingRequest)
	}
	if rf, ok := ret.Get(0).(func(app.FetchAppListingRequest) []*bean.JobContainer); ok {
		r0 = rf(fetchJobListingRequest)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*bean.JobContainer)
		}
	}

	if rf, ok := ret.Get(1).(func(app.FetchAppListingRequest) error); ok {
		r1 = rf(fetchJobListingRequest)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchMinDetailOtherEnvironment provides a mock function with given fields: appId
func (_m *AppListingService) FetchMinDetailOtherEnvironment(appId int) ([]*bean.Environment, error) {
	ret := _m.Called(appId)

	var r0 []*bean.Environment
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]*bean.Environment, error)); ok {
		return rf(appId)
	}
	if rf, ok := ret.Get(0).(func(int) []*bean.Environment); ok {
		r0 = rf(appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*bean.Environment)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchOtherEnvironment provides a mock function with given fields: ctx, appId
func (_m *AppListingService) FetchOtherEnvironment(ctx context.Context, appId int) ([]*bean.Environment, error) {
	ret := _m.Called(ctx, appId)

	var r0 []*bean.Environment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int) ([]*bean.Environment, error)); ok {
		return rf(ctx, appId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int) []*bean.Environment); ok {
		r0 = rf(ctx, appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*bean.Environment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int) error); ok {
		r1 = rf(ctx, appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchOverviewAppsByEnvironment provides a mock function with given fields: envId, limit, offset
func (_m *AppListingService) FetchOverviewAppsByEnvironment(envId int, limit int, offset int) (*app.OverviewAppsByEnvironmentBean, error) {
	ret := _m.Called(envId, limit, offset)

	var r0 *app.OverviewAppsByEnvironmentBean
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int, int) (*app.OverviewAppsByEnvironmentBean, error)); ok {
		return rf(envId, limit, offset)
	}
	if rf, ok := ret.Get(0).(func(int, int, int) *app.OverviewAppsByEnvironmentBean); ok {
		r0 = rf(envId, limit, offset)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*app.OverviewAppsByEnvironmentBean)
		}
	}

	if rf, ok := ret.Get(1).(func(int, int, int) error); ok {
		r1 = rf(envId, limit, offset)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchOverviewCiPipelines provides a mock function with given fields: jobId
func (_m *AppListingService) FetchOverviewCiPipelines(jobId int) ([]*bean.JobListingContainer, error) {
	ret := _m.Called(jobId)

	var r0 []*bean.JobListingContainer
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]*bean.JobListingContainer, error)); ok {
		return rf(jobId)
	}
	if rf, ok := ret.Get(0).(func(int) []*bean.JobListingContainer); ok {
		r0 = rf(jobId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*bean.JobListingContainer)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(jobId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetReleaseCount provides a mock function with given fields: appId, envId
func (_m *AppListingService) GetReleaseCount(appId int, envId int) (int, error) {
	ret := _m.Called(appId, envId)

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int) (int, error)); ok {
		return rf(appId, envId)
	}
	if rf, ok := ret.Get(0).(func(int, int) int); ok {
		r0 = rf(appId, envId)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(appId, envId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GraphAPI provides a mock function with given fields: appId, envId
func (_m *AppListingService) GraphAPI(appId int, envId int) error {
	ret := _m.Called(appId, envId)

	var r0 error
	if rf, ok := ret.Get(0).(func(int, int) error); ok {
		r0 = rf(appId, envId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ISLastReleaseStopType provides a mock function with given fields: appId, envId
func (_m *AppListingService) ISLastReleaseStopType(appId int, envId int) (bool, error) {
	ret := _m.Called(appId, envId)

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int) (bool, error)); ok {
		return rf(appId, envId)
	}
	if rf, ok := ret.Get(0).(func(int, int) bool); ok {
		r0 = rf(appId, envId)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(appId, envId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ISLastReleaseStopTypeV2 provides a mock function with given fields: pipelineIds
func (_m *AppListingService) ISLastReleaseStopTypeV2(pipelineIds []int) (map[int]bool, error) {
	ret := _m.Called(pipelineIds)

	var r0 map[int]bool
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) (map[int]bool, error)); ok {
		return rf(pipelineIds)
	}
	if rf, ok := ret.Get(0).(func([]int) map[int]bool); ok {
		r0 = rf(pipelineIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[int]bool)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(pipelineIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MemoryRequestGroupByContainer provides a mock function with given fields: podName, namespace, env, proEndpoint
func (_m *AppListingService) MemoryRequestGroupByContainer(podName string, namespace string, env string, proEndpoint string) map[string]string {
	ret := _m.Called(podName, namespace, env, proEndpoint)

	var r0 map[string]string
	if rf, ok := ret.Get(0).(func(string, string, string, string) map[string]string); ok {
		r0 = rf(podName, namespace, env, proEndpoint)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]string)
		}
	}

	return r0
}

// MemoryRequestGroupByPod provides a mock function with given fields: namespace, env, proEndpoint
func (_m *AppListingService) MemoryRequestGroupByPod(namespace string, env string, proEndpoint string) map[string]string {
	ret := _m.Called(namespace, env, proEndpoint)

	var r0 map[string]string
	if rf, ok := ret.Get(0).(func(string, string, string) map[string]string); ok {
		r0 = rf(namespace, env, proEndpoint)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]string)
		}
	}

	return r0
}

// MemoryUsageGroupByContainer provides a mock function with given fields: podName, namespace, env, proEndpoint
func (_m *AppListingService) MemoryUsageGroupByContainer(podName string, namespace string, env string, proEndpoint string) map[string]string {
	ret := _m.Called(podName, namespace, env, proEndpoint)

	var r0 map[string]string
	if rf, ok := ret.Get(0).(func(string, string, string, string) map[string]string); ok {
		r0 = rf(podName, namespace, env, proEndpoint)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]string)
		}
	}

	return r0
}

// MemoryUsageGroupByPod provides a mock function with given fields: namespace, env, proEndpoint
func (_m *AppListingService) MemoryUsageGroupByPod(namespace string, env string, proEndpoint string) map[string]string {
	ret := _m.Called(namespace, env, proEndpoint)

	var r0 map[string]string
	if rf, ok := ret.Get(0).(func(string, string, string) map[string]string); ok {
		r0 = rf(namespace, env, proEndpoint)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]string)
		}
	}

	return r0
}

// MemoryUsageGroupByPodGraph provides a mock function with given fields: podName, namespace, env, proEndpoint, r
func (_m *AppListingService) MemoryUsageGroupByPodGraph(podName string, namespace string, env string, proEndpoint string, r v1.Range) map[string][]interface{} {
	ret := _m.Called(podName, namespace, env, proEndpoint, r)

	var r0 map[string][]interface{}
	if rf, ok := ret.Get(0).(func(string, string, string, string, v1.Range) map[string][]interface{}); ok {
		r0 = rf(podName, namespace, env, proEndpoint, r)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string][]interface{})
		}
	}

	return r0
}

// PodCountByAppLabel provides a mock function with given fields: appLabel, namespace, env, proEndpoint
func (_m *AppListingService) PodCountByAppLabel(appLabel string, namespace string, env string, proEndpoint string) int {
	ret := _m.Called(appLabel, namespace, env, proEndpoint)

	var r0 int
	if rf, ok := ret.Get(0).(func(string, string, string, string) int); ok {
		r0 = rf(appLabel, namespace, env, proEndpoint)
	} else {
		r0 = ret.Get(0).(int)
	}

	return r0
}

// PodListByAppLabel provides a mock function with given fields: appLabel, namespace, env, proEndpoint
func (_m *AppListingService) PodListByAppLabel(appLabel string, namespace string, env string, proEndpoint string) map[string]string {
	ret := _m.Called(appLabel, namespace, env, proEndpoint)

	var r0 map[string]string
	if rf, ok := ret.Get(0).(func(string, string, string, string) map[string]string); ok {
		r0 = rf(appLabel, namespace, env, proEndpoint)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]string)
		}
	}

	return r0
}

// RedirectToLinkouts provides a mock function with given fields: Id, appId, envId, podName, containerName
func (_m *AppListingService) RedirectToLinkouts(Id int, appId int, envId int, podName string, containerName string) (string, error) {
	ret := _m.Called(Id, appId, envId, podName, containerName)

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int, int, string, string) (string, error)); ok {
		return rf(Id, appId, envId, podName, containerName)
	}
	if rf, ok := ret.Get(0).(func(int, int, int, string, string) string); ok {
		r0 = rf(Id, appId, envId, podName, containerName)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(int, int, int, string, string) error); ok {
		r1 = rf(Id, appId, envId, podName, containerName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewAppListingService creates a new instance of AppListingService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewAppListingService(t interface {
	mock.TestingT
	Cleanup(func())
}) *AppListingService {
	mock := &AppListingService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
