# PostHog App Analytics Metrics Implementation

## Overview
This document describes the implementation of new PostHog metrics for app analytics in Devtron's telemetry system.

## Implemented Metrics

### 1. Number of projects having 0 applications
- **Metric Name**: `projectsWithZeroAppsCount`
- **Description**: Counts the number of active projects (teams) that have no active applications
- **Query**: Counts teams where no active apps exist with that team_id
- **Mode**: FULL (requires app repository access)

### 2. Number of apps having propagation tags
- **Metric Name**: `appsWithPropagationTagsCount`
- **Description**: Counts apps that have at least one label with propagate=true
- **Query**: Counts distinct app_ids from app_label table where propagate=true
- **Mode**: FULL (requires app label repository access)

### 3. Number of apps having non-propagation tags
- **Metric Name**: `appsWithNonPropagationTagsCount`
- **Description**: Counts apps that have at least one label with propagate=false
- **Query**: Counts distinct app_ids from app_label table where propagate=false
- **Mode**: FULL (requires app label repository access)

### 4. Number of apps having description defined
- **Metric Name**: `appsWithDescriptionCount`
- **Description**: Counts active apps that have a non-empty description field
- **Query**: Counts apps where description IS NOT NULL AND TRIM(description) != ''
- **Mode**: FULL (requires app repository access)

### 5. Number of apps having catalog data
- **Metric Name**: `appsWithCatalogDataCount`
- **Description**: Counts installed apps that have catalog data (app store applications)
- **Query**: Counts distinct installed_apps through app_store_application_version
- **Mode**: FULL (requires app store repository access)

### 6. Number of apps having readme data
- **Metric Name**: `appsWithReadmeDataCount`
- **Description**: Counts installed apps that have non-empty readme data
- **Query**: Counts installed apps with non-empty readme in app_store_application_version
- **Mode**: FULL (requires app store repository access)

## Implementation Details

### Files Modified

1. **client/telemetry/telemetryQueriesExtended.go**
   - Added 6 new query methods for FULL mode telemetry
   - Each method follows the established pattern with error handling and debug logging

2. **client/telemetry/bean.go**
   - Added 6 new fields to TelemetryEventDto struct
   - Fields follow existing naming conventions with omitempty JSON tags

3. **client/telemetry/TelemetryEventClientExtended.go**
   - Added calls to new metric methods in the summary event collection
   - Metrics are collected only in FULL mode

### Mode Classification: FULL Mode

All metrics are classified as FULL mode because they require:
- Complex database queries across multiple tables
- Access to app repository, app label repository, and app store repositories
- Dependencies that are not available in EA mode

This follows the established pattern where:
- **EA Mode**: Basic metrics with minimal dependencies
- **FULL Mode**: Advanced metrics requiring complex repository access

### Database Schema Dependencies

- **team table**: For project counting
- **app table**: For app counting and description checks
- **app_label table**: For tag/label analysis
- **installed_apps table**: For app store app counting
- **installed_app_versions table**: For active app store versions
- **app_store_application_version table**: For catalog and readme data

### Error Handling

All methods follow the established pattern:
- Return -1 on error to indicate failure
- Log errors with appropriate context
- Log debug information for successful counts
- Use database connection from app repository

### Performance Considerations

- Queries use appropriate JOINs and WHERE clauses for efficiency
- DISTINCT is used where necessary to avoid double counting
- Active flags are checked to exclude deleted/inactive records
- Indexes on foreign keys should support these queries efficiently

## Testing Recommendations

1. **Unit Tests**: Test each query method with mock data
2. **Integration Tests**: Verify metrics are collected in FULL mode
3. **Performance Tests**: Ensure queries perform well with large datasets
4. **Data Validation**: Verify counts match expected values in test environments

## Future Enhancements

Consider adding:
- Time-based metrics (apps created in last 30 days, etc.)
- App type breakdown (DevtronApp vs Job vs ExternalApp)
- Tag usage analytics (most common tags, tag distribution)
- App lifecycle metrics (apps with CI/CD configured)
