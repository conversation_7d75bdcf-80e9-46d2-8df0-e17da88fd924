openapi: "3.0.0"
info:
  version: 1.0.0
  title: Devtron
servers:
  - url: http://petstore.swagger.io/api
paths:
  /user/v2:
    get:
      summary: Returns all users
      description: all the template users
      operationId: GetAllV2
      parameters:

        - name: searchKey
          in: query
          description: Search key for user listing
          required: false
          schema:
            type: string

        - name: sortOrder
          in: query
          description: Sorting order (ASC or DESC)
          required: false
          schema:
            type: string
            enum:
              - ASC
              - DESC

        - name: sortBy
          in: query
          description: Sorting by email_id or last_login
          required: false
          schema:
            type: string
            enum:
              - email_id
              - last_login

        - name: offset
          in: query
          description: Offset for paginating the results
          required: false
          schema:
            type: integer

        - name: size
          in: query
          description: Size of the result set
          required: false
          schema:
            type: integer

        - name: showAll
          in: query
          description: Show all users (boolean)
          required: false
          schema:
            type: boolean

      responses:
        '200':
          description: list response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserListingResponse'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /user:
    get:
      summary: Returns all users
      description: all the template users
      operationId: findAllUsers
      deprecated: true   # Marking the operation as deprecated
      responses:
        '200':
          description: list response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AllUsers'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    post:
      summary: Creates a new User
      description: create user api, with multiple environment in one row of policy, plus chart group additional type of policy.
      operationId: addUser
      requestBody:
        description: json as request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
      responses:
        '200':
          description: create user response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    put:
      summary: update a user
      description: Updates a new user in the system
      operationId: updateUser
      requestBody:
        description: json as request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
      responses:
        '200':
          description: user response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /user/email:
    get:
      summary: search a user list by name
      description: search user detail by name
      operationId: findUserByEmail
      parameters:
        - name: email-id
          in: query
          description: ID of pet to delete
          required: true
          schema:
            type: string
      responses:
        '200':
          description: list response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /user/{id}:
    get:
      summary: Returns user detail with role filter
      description: all the template group policies
      operationId: findUserById
      parameters:
        - name: id
          in: path
          description: ID of user id
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: user detail response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /orchestrator/user/bulk:
    delete:
      summary: Delete multiple users in bulk
      description: Deletes user entities in bulk based on the provided criteria.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkDeleteRequest'
      responses:
        '200':
          description: Successfully deleted users
          content:
            application/json:
              schema:
                type: boolean
        '400':
          description: Bad request, invalid input
        '404':
          description: Users not found
        '500':
          description: Internal server error


components:
  schemas:
    User:
      type: object
      required:
        - email_id
      properties:
        id:
          type: integer
          description: Unique id of user
        email_id:
          type: string
          description: Unique valid email-id of user, comma separated emails ids for multiple users
        userRoleGroups:
          type: array
          items:
            $ref: '#/components/schemas/UserRoleGroupItem'
        roleFilters:
          type: array
          items:
            $ref: '#/components/schemas/roleFilter'
          description: role filters objects
    UserListingResponse:
      type: object
      properties:
        users:
          items:
            $ref: '#/components/schemas/AllUsersV2'
          description: role filters objects
        totalCount:
          type: integer
          description: total number of results satisfying the conditions

    AllUsers:
      type: object
      required:
        - email_id
      properties:
        id:
          type: integer
          description: Unique id of user
        email_id:
          type: string
          description: Unique valid email-id of user, comma separated emails ids for multiple users
        groups:
          type: array
          items:
            type: string
        roleFilters:
          type: array
          items:
            $ref: '#/components/schemas/emptyRoleFilter'
          description: role filters objects
        lastLogin:
          type: string
          format: date-time
          description: user last login time
    AllUsersV2:
      type: object
      required:
        - email_id
      properties:
        id:
          type: integer
          description: Unique id of user
        email_id:
          type: string
          description: Unique valid email-id of user, comma separated emails ids for multiple users
        userRoleGroups:
          type: array
          items:
            $ref: '#/components/schemas/UserRoleGroupItem'
        lastLogin:
          type: string
          format: date-time
          description: user last login time

    emptyRoleFilter:
      type: object
      required:
        - action


    roleFilter:
      type: object
      required:
        - action
      properties:
        entity:
          type: string
          description: global entity name, i.e chart-group, git, docker, cluster etc. if this key is present than field team, application and environment are ignored, here empty entity means Devtron apps permission.
          enum:
            - chart-group
            - docker
            - git
            - cluster
            - notification
        team:
          type: string
          description: team name
        entityName:
          type: string
          description: global entity name item name, i.e chart-group ("abc"), git("devtron-gt") etc.
        environment:
          type: string
          description: comma saperated environments names.
        action:
          type: string
          description: action is type of role, i.e manager, admin, trigger, view, etc.
        accessType:
          type: string
          enum: ["", "helm-app"]
          description: accessType difine permission type "devtron-app"=devtron app work flow, "helm-app"=helm app work flow. based on this flag data categoriesed into devtron and helm permission tabs in user auth section.
    BulkDeleteRequest:
      type: object
      properties:
        ids:
          type: array
          items:
            type: integer
            format: int32
          description: Array of user IDs to be deleted
        listingRequest:
          $ref: '#/components/schemas/ListingRequest'
    ListingRequest:
      type: object
      properties:
        searchKey:
          type: string
          description: Search key for filtering
        sortOrder:
          type: string
          enum:
            - ASC
            - DESC
          description: Sorting order
        sortBy:
          type: string
          enum:
            - email_id
            - last_login
          description: Attribute to sort by
        offset:
          type: integer
          format: int32
          description: Starting index for fetching listings
        size:
          type: integer
          format: int32
          description: Number of listings to fetch
        showAll:
          type: boolean
          description: Show all listings
    UserRoleGroupItem:
      type: object
      properties:
        roleGroup:
          $ref: '#/components/schemas/RoleGroup'
    RoleGroup:
      type: object
      properties:
        id:
          type: integer
          format: int32
          description: The ID of the role group
        name:
          type: string
          description: The name of the role group
        description:
          type: string
          description: The description of the role group

    Error:
      required:
        - code
        - message
      properties:
        code:
          type: integer
          format: int32
          description: Error code
        message:
          type: string
          description: Error message