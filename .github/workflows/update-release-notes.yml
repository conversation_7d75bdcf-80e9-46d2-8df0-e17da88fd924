name: update-release-notes

# Controls when the workflow will run
on:
  pull_request_target:
    types:
      - closed
    branches:
      - main
      - develop

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  if_merged:
    if: github.event.pull_request.merged == true
    runs-on: ubuntu-latest
    steps:
    - run: |
        echo "PR ${{ github.event.pull_request.title }} (#${{ github.event.pull_request.number }}) has been merged"
        closed_pr="- ${{ github.event.pull_request.title }} (#${{ github.event.pull_request.number }})"
        closed_pr_lower=$(echo "$closed_pr" | sed -e 's/\(.*\)/\L\1/')
        gh repo clone $GITHUB_REPOSITORY
        REPO_NAME=$(echo $GITHUB_REPOSITORY | awk -F ["/"] '{print $2}')
        DOC_SECTION=${DOC_SECTION:=Documentation}
        FEAT_SECTION=${FEAT_SECTION:=Enhancements}
        BUG_SECTION=${BUG_SECTION:=Bugs}
        OTHER_SECTION=${OTHER_SECTION:=Others}
        FILE_NAME=${FILE_NAME:=beta-releasenotes.md}
        RELEASE_NOTES_BRANCH=${RELEASE_NOTES_BRANCH:=release-bot}
        echo "Cloned repository $REPO_NAME"
        cd $REPO_NAME
        git config --global user.email "${GIT_CONFIG_EMAIL}" && git config --global user.name "${GIT_CONFIG_NAME}"
        git branch ${RELEASE_NOTES_BRANCH} refs/remotes/origin/${RELEASE_NOTES_BRANCH}
        git checkout ${RELEASE_NOTES_BRANCH}
        # Update Documentation PRs in documentation section
        if [[ "$closed_pr_lower" == *"doc:"* || "$closed_pr_lower" == *"docs:"* ]]
        then
        sed -i "/${DOC_SECTION}/a $closed_pr" "$FILE_NAME"
        # Skip Release PRs from release notes
        elif [[ "$closed_pr_lower" == *"release:"* || "$closed_pr_lower" == *"releases:"* ]]
        then
        echo "Skipping Release PR: $closed_pr"
        # Update Bug fixes PRs in Bugs section
        elif [[ "$closed_pr_lower" == *"fix:"* || "$closed_pr_lower" == *"fixes:"* ]]
        then
        sed -i "/${BUG_SECTION}/a $closed_pr" "$FILE_NAME"
        # Update Features/Enhancements PRs in enhancement section
        elif [[ "$closed_pr_lower" == *"feat:"* || "$closed_pr_lower" == *"feature:"* || "$closed_pr_lower" == *"enhancement:"* || "$closed_pr_lower" == *"perf:"* ]]
        then
        sed -i "/${FEAT_SECTION}/a $closed_pr" "$FILE_NAME"
        else
        # Update all other PRs in others section
        sed -i "/${OTHER_SECTION}/a $closed_pr" "$FILE_NAME"
        fi
        git commit -am "Updated release notes"
        git push https://${GIT_CONFIG_NAME}:${GH_TOKEN}@github.com/${GITHUB_REPOSITORY} $RELEASE_NOTES_BRANCH
      env:
        GH_TOKEN: ${{ secrets.GH_SYSTEMSDT_TOKEN }}
        GIT_CONFIG_NAME: ${{ vars.GH_SYSTEMSDT_USERNAME }}
        GIT_CONFIG_EMAIL: ${{ secrets.GH_SYSTEMSDT_EMAIL }}
