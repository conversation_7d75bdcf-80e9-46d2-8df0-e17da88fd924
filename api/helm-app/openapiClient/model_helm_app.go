/*
Devtron Labs

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

API version: 1.0.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package openapi

import (
	"encoding/json"
	"time"
)

// HelmApp struct for HelmApp
type HelmApp struct {
	// time when this application was last deployed/updated
	LastDeployedAt *time.Time `json:"lastDeployedAt,omitempty"`
	// name of the helm application/helm release name
	AppName *string `json:"appName,omitempty"`
	// unique identifier for app
	AppId *string `json:"appId,omitempty"`
	// name of the chart
	ChartName *string `json:"chartName,omitempty"`
	// url/location of the chart icon
	ChartAvatar *string `json:"chartAvatar,omitempty"`
	// unique identifier for the project, APP with no project will have id `0`
	ProjectId *int32 `json:"projectId,omitempty"`
	// chart version
	ChartVersion      *string               `json:"chartVersion,omitempty"`
	EnvironmentDetail *AppEnvironmentDetail `json:"environmentDetail,omitempty"`
	AppStatus         *string               `json:"appStatus,omitempty"`
}

// NewHelmApp instantiates a new HelmApp object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewHelmApp() *HelmApp {
	this := HelmApp{}
	return &this
}

// NewHelmAppWithDefaults instantiates a new HelmApp object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewHelmAppWithDefaults() *HelmApp {
	this := HelmApp{}
	return &this
}

// GetLastDeployedAt returns the LastDeployedAt field value if set, zero value otherwise.
func (o *HelmApp) GetLastDeployedAt() time.Time {
	if o == nil || o.LastDeployedAt == nil {
		var ret time.Time
		return ret
	}
	return *o.LastDeployedAt
}

// GetLastDeployedAtOk returns a tuple with the LastDeployedAt field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *HelmApp) GetLastDeployedAtOk() (*time.Time, bool) {
	if o == nil || o.LastDeployedAt == nil {
		return nil, false
	}
	return o.LastDeployedAt, true
}

// HasLastDeployedAt returns a boolean if a field has been set.
func (o *HelmApp) HasLastDeployedAt() bool {
	if o != nil && o.LastDeployedAt != nil {
		return true
	}

	return false
}

// SetLastDeployedAt gets a reference to the given time.Time and assigns it to the LastDeployedAt field.
func (o *HelmApp) SetLastDeployedAt(v time.Time) {
	o.LastDeployedAt = &v
}

// GetAppName returns the AppName field value if set, zero value otherwise.
func (o *HelmApp) GetAppName() string {
	if o == nil || o.AppName == nil {
		var ret string
		return ret
	}
	return *o.AppName
}

// GetAppNameOk returns a tuple with the AppName field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *HelmApp) GetAppNameOk() (*string, bool) {
	if o == nil || o.AppName == nil {
		return nil, false
	}
	return o.AppName, true
}

// HasAppName returns a boolean if a field has been set.
func (o *HelmApp) HasAppName() bool {
	if o != nil && o.AppName != nil {
		return true
	}

	return false
}

// SetAppName gets a reference to the given string and assigns it to the AppName field.
func (o *HelmApp) SetAppName(v string) {
	o.AppName = &v
}

// GetAppId returns the AppId field value if set, zero value otherwise.
func (o *HelmApp) GetAppId() string {
	if o == nil || o.AppId == nil {
		var ret string
		return ret
	}
	return *o.AppId
}

// GetAppIdOk returns a tuple with the AppId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *HelmApp) GetAppIdOk() (*string, bool) {
	if o == nil || o.AppId == nil {
		return nil, false
	}
	return o.AppId, true
}

// HasAppId returns a boolean if a field has been set.
func (o *HelmApp) HasAppId() bool {
	if o != nil && o.AppId != nil {
		return true
	}

	return false
}

// SetAppId gets a reference to the given string and assigns it to the AppId field.
func (o *HelmApp) SetAppId(v string) {
	o.AppId = &v
}

// GetChartName returns the ChartName field value if set, zero value otherwise.
func (o *HelmApp) GetChartName() string {
	if o == nil || o.ChartName == nil {
		var ret string
		return ret
	}
	return *o.ChartName
}

// GetChartNameOk returns a tuple with the ChartName field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *HelmApp) GetChartNameOk() (*string, bool) {
	if o == nil || o.ChartName == nil {
		return nil, false
	}
	return o.ChartName, true
}

// HasChartName returns a boolean if a field has been set.
func (o *HelmApp) HasChartName() bool {
	if o != nil && o.ChartName != nil {
		return true
	}

	return false
}

// SetChartName gets a reference to the given string and assigns it to the ChartName field.
func (o *HelmApp) SetChartName(v string) {
	o.ChartName = &v
}

// GetChartAvatar returns the ChartAvatar field value if set, zero value otherwise.
func (o *HelmApp) GetChartAvatar() string {
	if o == nil || o.ChartAvatar == nil {
		var ret string
		return ret
	}
	return *o.ChartAvatar
}

// GetChartAvatarOk returns a tuple with the ChartAvatar field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *HelmApp) GetChartAvatarOk() (*string, bool) {
	if o == nil || o.ChartAvatar == nil {
		return nil, false
	}
	return o.ChartAvatar, true
}

// HasChartAvatar returns a boolean if a field has been set.
func (o *HelmApp) HasChartAvatar() bool {
	if o != nil && o.ChartAvatar != nil {
		return true
	}

	return false
}

// SetChartAvatar gets a reference to the given string and assigns it to the ChartAvatar field.
func (o *HelmApp) SetChartAvatar(v string) {
	o.ChartAvatar = &v
}

// GetProjectId returns the ProjectId field value if set, zero value otherwise.
func (o *HelmApp) GetProjectId() int32 {
	if o == nil || o.ProjectId == nil {
		var ret int32
		return ret
	}
	return *o.ProjectId
}

// GetProjectIdOk returns a tuple with the ProjectId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *HelmApp) GetProjectIdOk() (*int32, bool) {
	if o == nil || o.ProjectId == nil {
		return nil, false
	}
	return o.ProjectId, true
}

// HasProjectId returns a boolean if a field has been set.
func (o *HelmApp) HasProjectId() bool {
	if o != nil && o.ProjectId != nil {
		return true
	}

	return false
}

// SetProjectId gets a reference to the given int32 and assigns it to the ProjectId field.
func (o *HelmApp) SetProjectId(v int32) {
	o.ProjectId = &v
}

// GetChartVersion returns the ChartVersion field value if set, zero value otherwise.
func (o *HelmApp) GetChartVersion() string {
	if o == nil || o.ChartVersion == nil {
		var ret string
		return ret
	}
	return *o.ChartVersion
}

// GetChartVersionOk returns a tuple with the ChartVersion field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *HelmApp) GetChartVersionOk() (*string, bool) {
	if o == nil || o.ChartVersion == nil {
		return nil, false
	}
	return o.ChartVersion, true
}

// HasChartVersion returns a boolean if a field has been set.
func (o *HelmApp) HasChartVersion() bool {
	if o != nil && o.ChartVersion != nil {
		return true
	}

	return false
}

// SetChartVersion gets a reference to the given string and assigns it to the ChartVersion field.
func (o *HelmApp) SetChartVersion(v string) {
	o.ChartVersion = &v
}

// GetEnvironmentDetail returns the EnvironmentDetail field value if set, zero value otherwise.
func (o *HelmApp) GetEnvironmentDetail() AppEnvironmentDetail {
	if o == nil || o.EnvironmentDetail == nil {
		var ret AppEnvironmentDetail
		return ret
	}
	return *o.EnvironmentDetail
}

// GetEnvironmentDetailOk returns a tuple with the EnvironmentDetail field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *HelmApp) GetEnvironmentDetailOk() (*AppEnvironmentDetail, bool) {
	if o == nil || o.EnvironmentDetail == nil {
		return nil, false
	}
	return o.EnvironmentDetail, true
}

// HasEnvironmentDetail returns a boolean if a field has been set.
func (o *HelmApp) HasEnvironmentDetail() bool {
	if o != nil && o.EnvironmentDetail != nil {
		return true
	}

	return false
}

// SetEnvironmentDetail gets a reference to the given AppEnvironmentDetail and assigns it to the EnvironmentDetail field.
func (o *HelmApp) SetEnvironmentDetail(v AppEnvironmentDetail) {
	o.EnvironmentDetail = &v
}

// GetAppStatus returns the AppStatus field value if set, zero value otherwise.
func (o *HelmApp) GetAppStatus() string {
	if o == nil || o.AppStatus == nil {
		var ret string
		return ret
	}
	return *o.AppStatus
}

// GetAppStatusOk returns a tuple with the AppStatus field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *HelmApp) GetAppStatusOk() (*string, bool) {
	if o == nil || o.AppStatus != nil {
		return nil, false
	}
	return o.AppStatus, true
}

// HasAppStatus returns a boolean if a field has been set.
func (o *HelmApp) HasAppStatus() bool {
	if o != nil && o.AppStatus != nil {
		return true
	}

	return false
}

// SetAppStatus gets a reference to the given string and assigns it to the AppStatus field.
func (o *HelmApp) SetAppStatus(v string) {
	o.AppStatus = &v
}

func (o HelmApp) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o HelmApp) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if o.LastDeployedAt != nil {
		toSerialize["lastDeployedAt"] = o.LastDeployedAt
	}
	if o.AppName != nil {
		toSerialize["appName"] = o.AppName
	}
	if o.AppId != nil {
		toSerialize["appId"] = o.AppId
	}
	if o.ChartName != nil {
		toSerialize["chartName"] = o.ChartName
	}
	if o.ChartAvatar != nil {
		toSerialize["chartAvatar"] = o.ChartAvatar
	}
	if o.ProjectId != nil {
		toSerialize["projectId"] = o.ProjectId
	}
	if o.ChartVersion != nil {
		toSerialize["chartVersion"] = o.ChartVersion
	}
	if o.EnvironmentDetail != nil {
		toSerialize["environmentDetail"] = o.EnvironmentDetail
	}
	if o.AppStatus != nil {
		toSerialize["appStatus"] = o.AppStatus
	}
	return toSerialize, nil
}

type NullableHelmApp struct {
	value *HelmApp
	isSet bool
}

func (v NullableHelmApp) Get() *HelmApp {
	return v.value
}

func (v *NullableHelmApp) Set(val *HelmApp) {
	v.value = val
	v.isSet = true
}

func (v NullableHelmApp) IsSet() bool {
	return v.isSet
}

func (v *NullableHelmApp) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableHelmApp(val *HelmApp) *NullableHelmApp {
	return &NullableHelmApp{value: val, isSet: true}
}

func (v NullableHelmApp) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableHelmApp) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
