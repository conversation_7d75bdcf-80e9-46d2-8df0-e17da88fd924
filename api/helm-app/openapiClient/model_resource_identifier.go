/*
Devtron Labs

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

API version: 1.0.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package openapi

import (
	"encoding/json"
)

// ResourceIdentifier struct for ResourceIdentifier
type ResourceIdentifier struct {
	// k8s resource group
	Group *string `json:"group,omitempty"`
	// k8s resource kind
	Kind *string `json:"kind,omitempty"`
	// k8s resource version
	Version *string `json:"version,omitempty"`
	// k8s resource name
	Name *string `json:"name,omitempty"`
	// k8s resource ns
	Namespace *string `json:"namespace,omitempty"`
}

// NewResourceIdentifier instantiates a new ResourceIdentifier object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewResourceIdentifier() *ResourceIdentifier {
	this := ResourceIdentifier{}
	return &this
}

// NewResourceIdentifierWithDefaults instantiates a new ResourceIdentifier object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewResourceIdentifierWithDefaults() *ResourceIdentifier {
	this := ResourceIdentifier{}
	return &this
}

// GetGroup returns the Group field value if set, zero value otherwise.
func (o *ResourceIdentifier) GetGroup() string {
	if o == nil || o.Group == nil {
		var ret string
		return ret
	}
	return *o.Group
}

// GetGroupOk returns a tuple with the Group field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ResourceIdentifier) GetGroupOk() (*string, bool) {
	if o == nil || o.Group == nil {
		return nil, false
	}
	return o.Group, true
}

// HasGroup returns a boolean if a field has been set.
func (o *ResourceIdentifier) HasGroup() bool {
	if o != nil && o.Group != nil {
		return true
	}

	return false
}

// SetGroup gets a reference to the given string and assigns it to the Group field.
func (o *ResourceIdentifier) SetGroup(v string) {
	o.Group = &v
}

// GetKind returns the Kind field value if set, zero value otherwise.
func (o *ResourceIdentifier) GetKind() string {
	if o == nil || o.Kind == nil {
		var ret string
		return ret
	}
	return *o.Kind
}

// GetKindOk returns a tuple with the Kind field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ResourceIdentifier) GetKindOk() (*string, bool) {
	if o == nil || o.Kind == nil {
		return nil, false
	}
	return o.Kind, true
}

// HasKind returns a boolean if a field has been set.
func (o *ResourceIdentifier) HasKind() bool {
	if o != nil && o.Kind != nil {
		return true
	}

	return false
}

// SetKind gets a reference to the given string and assigns it to the Kind field.
func (o *ResourceIdentifier) SetKind(v string) {
	o.Kind = &v
}

// GetVersion returns the Version field value if set, zero value otherwise.
func (o *ResourceIdentifier) GetVersion() string {
	if o == nil || o.Version == nil {
		var ret string
		return ret
	}
	return *o.Version
}

// GetVersionOk returns a tuple with the Version field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ResourceIdentifier) GetVersionOk() (*string, bool) {
	if o == nil || o.Version == nil {
		return nil, false
	}
	return o.Version, true
}

// HasVersion returns a boolean if a field has been set.
func (o *ResourceIdentifier) HasVersion() bool {
	if o != nil && o.Version != nil {
		return true
	}

	return false
}

// SetVersion gets a reference to the given string and assigns it to the Version field.
func (o *ResourceIdentifier) SetVersion(v string) {
	o.Version = &v
}

// GetName returns the Name field value if set, zero value otherwise.
func (o *ResourceIdentifier) GetName() string {
	if o == nil || o.Name == nil {
		var ret string
		return ret
	}
	return *o.Name
}

// GetNameOk returns a tuple with the Name field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ResourceIdentifier) GetNameOk() (*string, bool) {
	if o == nil || o.Name == nil {
		return nil, false
	}
	return o.Name, true
}

// HasName returns a boolean if a field has been set.
func (o *ResourceIdentifier) HasName() bool {
	if o != nil && o.Name != nil {
		return true
	}

	return false
}

// SetName gets a reference to the given string and assigns it to the Name field.
func (o *ResourceIdentifier) SetName(v string) {
	o.Name = &v
}

// GetNamespace returns the Namespace field value if set, zero value otherwise.
func (o *ResourceIdentifier) GetNamespace() string {
	if o == nil || o.Namespace == nil {
		var ret string
		return ret
	}
	return *o.Namespace
}

// GetNamespaceOk returns a tuple with the Namespace field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ResourceIdentifier) GetNamespaceOk() (*string, bool) {
	if o == nil || o.Namespace == nil {
		return nil, false
	}
	return o.Namespace, true
}

// HasNamespace returns a boolean if a field has been set.
func (o *ResourceIdentifier) HasNamespace() bool {
	if o != nil && o.Namespace != nil {
		return true
	}

	return false
}

// SetNamespace gets a reference to the given string and assigns it to the Namespace field.
func (o *ResourceIdentifier) SetNamespace(v string) {
	o.Namespace = &v
}

func (o ResourceIdentifier) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Group != nil {
		toSerialize["group"] = o.Group
	}
	if o.Kind != nil {
		toSerialize["kind"] = o.Kind
	}
	if o.Version != nil {
		toSerialize["version"] = o.Version
	}
	if o.Name != nil {
		toSerialize["name"] = o.Name
	}
	if o.Namespace != nil {
		toSerialize["namespace"] = o.Namespace
	}
	return json.Marshal(toSerialize)
}

type NullableResourceIdentifier struct {
	value *ResourceIdentifier
	isSet bool
}

func (v NullableResourceIdentifier) Get() *ResourceIdentifier {
	return v.value
}

func (v *NullableResourceIdentifier) Set(val *ResourceIdentifier) {
	v.value = val
	v.isSet = true
}

func (v NullableResourceIdentifier) IsSet() bool {
	return v.isSet
}

func (v *NullableResourceIdentifier) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableResourceIdentifier(val *ResourceIdentifier) *NullableResourceIdentifier {
	return &NullableResourceIdentifier{value: val, isSet: true}
}

func (v NullableResourceIdentifier) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableResourceIdentifier) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


