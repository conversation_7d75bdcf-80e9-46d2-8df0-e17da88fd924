# Documentation for Devtron Labs

<a name="documentation-for-api-endpoints"></a>
## Documentation for API Endpoints

All URIs are relative to *http://localhost*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
*DefaultApi* | [**orchestratorAppListGet**](Apis/DefaultApi.md#orchestratorapplistget) | **GET** /orchestrator/app/list/ | this api gives all devtron applications.
*DefaultApi* | [**orchestratorApplicationClusterEnvDetailsGet**](Apis/DefaultApi.md#orchestratorapplicationclusterenvdetailsget) | **GET** /orchestrator/application/cluster-env-details | returns cluster environment namespace mappings
*DefaultApi* | [**orchestratorApplicationGet**](Apis/DefaultApi.md#orchestratorapplicationget) | **GET** /orchestrator/application/ | this api gives all external application+ devtron helm chart applications.


<a name="documentation-for-models"></a>
## Documentation for Models

 - [AppEnvironmentDetail](./Models/AppEnvironmentDetail.md)
 - [AppEnvironmentDetail_allOf](./Models/AppEnvironmentDetail_allOf.md)
 - [AppList](./Models/AppList.md)
 - [ClusterEnvironmentDetail](./Models/ClusterEnvironmentDetail.md)
 - [DevtronApp](./Models/DevtronApp.md)
 - [EnvironmentDetail](./Models/EnvironmentDetail.md)
 - [HelmApp](./Models/HelmApp.md)
 - [appListRequest](./Models/appListRequest.md)


<a name="documentation-for-authorization"></a>
## Documentation for Authorization

All endpoints do not require authorization.
